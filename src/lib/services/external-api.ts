import crypto from 'crypto';

/**
 * External API service for sending webhook data
 * Handles communication with external services for pull request processing
 */
export class ExternalAPIService {
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly timeoutMs: number;
  private readonly maxRetries: number;
  private readonly webhookSecret: string;

  constructor() {
    this.apiKey = process.env.EXTERNAL_API_KEY || '';
    this.baseUrl = process.env.EXTERNAL_API_BASE_URL || 'http://0.0.0.0:8000';
    this.timeoutMs = parseInt(process.env.WEBHOOK_TIMEOUT_MS || '10000');
    this.maxRetries = parseInt(process.env.WEBHOOK_RETRY_ATTEMPTS || '3');
    this.webhookSecret = process.env.EXTERNAL_WEBHOOK_SECRET || '';
  }

  /**
   * Generate GitHub webhook signature
   * @param payload - The payload to sign
   * @returns The signature string
   */
  private generateWebhookSignature(payload: string): string {
    if (!this.webhookSecret) {
      console.warn('EXTERNAL_WEBHOOK_SECRET not configured, signature will be empty');
      return '';
    }

    const signature = crypto
      .createHmac('sha256', this.webhookSecret)
      .update(payload)
      .digest('hex');

    return `sha256=${signature}`;
  }

  /**
   * Send pull request data to external API
   * @param data - Pull request data to send
   * @returns Promise<boolean> - true if successful, false otherwise
   */
  async sendPullRequestData(data: any): Promise<boolean> {
    if (!this.apiKey) {
      console.warn('EXTERNAL_API_KEY not configured, skipping API call');
      return false;
    }

    try {
      const controller = new AbortController();
      const payload = JSON.stringify(data);
      const signature = this.generateWebhookSignature(payload);

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'X-GitHub-Event': data.github_event || 'pull_request',
        'X-GitHub-Delivery': data.github_delivery || '',
      };

      // Add signature header if webhook secret is configured
      if (signature) {
        headers['X-Hub-Signature-256'] = signature;
      }

      const response = await fetch(`${this.baseUrl}/api/v1/github/pr_open`, {
        method: 'POST',
        headers,
        body: payload,
        signal: controller.signal
      });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unable to read error response');
        console.error(`External API Error ${response.status}: ${errorText}`);
        return false;
      }

      return true;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('External API call timed out');
      } else {
        console.error('External API call failed:', error);
      }
      return false;
    }
  }

  /**
   * Send pull request data with retry logic
   * @param data - Pull request data to send
   * @returns Promise<boolean> - true if successful after retries, false otherwise
   */
  async sendPullRequestDataWithRetry(data: any): Promise<boolean> {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      const success = await this.sendPullRequestData(data);

      if (success) {
        console.log(`Successfully sent PR data to API (attempt ${attempt})`);
        return true;
      }

      if (attempt < this.maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        console.log(`Retrying API call in ${delay}ms (attempt ${attempt}/${this.maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    console.error(`Failed to send PR data after ${this.maxRetries} attempts`);
    return false;
  }

  /**
   * Check if the external API is configured
   * @returns boolean - true if API key is configured
   */
  isConfigured(): boolean {
    return !!this.apiKey;
  }

  /**
   * Check if webhook signature is configured
   * @returns boolean - true if webhook secret is configured
   */
  isWebhookSecretConfigured(): boolean {
    return !!this.webhookSecret;
  }

  /**
   * Get current configuration (without sensitive data)
   * @returns object with configuration details
   */
  getConfig() {
    return {
      baseUrl: this.baseUrl,
      timeoutMs: this.timeoutMs,
      maxRetries: this.maxRetries,
      isConfigured: this.isConfigured(),
      isWebhookSecretConfigured: this.isWebhookSecretConfigured()
    };
  }
}

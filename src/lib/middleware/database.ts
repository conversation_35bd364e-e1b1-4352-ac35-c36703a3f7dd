/**
 * Database Middleware
 * Ensures database connection is established before API routes execute
 */

import { NextRequest, NextResponse } from 'next/server';
import { databaseService } from '@/src/lib/database/service';

/**
 * Database connection middleware for API routes
 * Ensures database connection is established before proceeding
 */
export async function withDatabaseConnection<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return async (...args: T): Promise<NextResponse> => {
    try {
      // Ensure database connection is established
      await databaseService.ensureConnection();
      
      // Proceed with the original handler
      return await handler(...args);
    } catch (error) {
      console.error('Database connection middleware error:', error);
      
      // Return a proper error response
      return NextResponse.json(
        {
          error: 'Database connection failed',
          message: 'Unable to establish database connection. Please try again.',
          timestamp: new Date().toISOString(),
        },
        { status: 503 }
      );
    }
  };
}

/**
 * Higher-order function to wrap API route handlers with database connection
 * Usage: export const GET = withDatabase(async (request) => { ... })
 */
export function withDatabase<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return withDatabaseConnection(handler);
}

/**
 * Database connection wrapper for webhook handlers
 * Ensures database connection for webhook processing
 */
export async function withDatabaseForWebhook<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      // Ensure database connection is established
      await databaseService.ensureConnection();
      
      // Proceed with the original handler
      return await handler(...args);
    } catch (error) {
      console.error('Database connection webhook middleware error:', error);
      throw error; // Re-throw for webhook handlers to handle appropriately
    }
  };
}

/**
 * Utility function to check database health
 * Can be used in health check endpoints
 */
export async function checkDatabaseHealth() {
  try {
    const health = await databaseService.getHealth();
    return {
      status: health.status === 'healthy' ? 'ok' : 'error',
      message: health.message,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown database error',
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * Database connection status for monitoring
 */
export function getDatabaseConnectionStatus() {
  return databaseService.getConnectionStats();
}
